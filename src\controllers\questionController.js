const Question = require('../models/Question');
const Company = require('../models/Company');
const Test = require('../models/Test');
const logger = require('../config/logger');
const mongoose = require('mongoose');

// Input validation helper
const validateQuestionData = (data) => {
    const errors = [];

    if (!data.questionText || data.questionText.trim().length < 5) {
        errors.push('Question text must be at least 5 characters long');
    }

    // Match the schema exactly
    const validTypes = ['MCQ', 'Multiple-Select', 'Short-Answer', 'Code'];
    if (!data.questionType || !validTypes.includes(data.questionType)) {
        errors.push('Valid question type is required');
    }

    if (!data.category || data.category.trim().length < 2) {
        errors.push('Category is required');
    }

    const validDifficulties = ['Easy', 'Medium', 'Hard'];
    if (!data.difficulty || !validDifficulties.includes(data.difficulty)) {
        errors.push('Valid difficulty level is required');
    }

    // Check MCQ-specific conditions
    if (data.questionType === 'MCQ' || data.questionType === 'Multiple-Select') {
        if (!Array.isArray(data.options) || data.options.length < 2) {
            errors.push('MCQ/Multiple-Select questions must have at least 2 options');
        }
        const hasCorrect = data.options.some(opt => opt.isCorrect === true);
        if (!hasCorrect) {
            errors.push('At least one correct option must be specified');
        }
    }

    if (data.timeLimit && (data.timeLimit < 30 || data.timeLimit > 3600)) {
        errors.push('Time limit must be between 30 seconds and 1 hour');
    }

    return errors;
};
const createQuestion = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Validate input data
        const validationErrors = validateQuestionData(req.body);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        const {
            questionText,
            questionType,
            category,
            difficulty,
            options,
            correctAnswer,
            explanation,
            points,
            isActive
        } = req.body;

        const trimmedText = questionText.trim();

        // Enhanced duplicate check: same companyId + questionText + questionType + category
        const existingQuestion = await Question.findOne({
            companyId: company._id,
            questionText: trimmedText,
            questionType,
            category
        });

        if (existingQuestion) {
            return res.status(400).json({ error: 'A similar question with same text, type and category already exists.' });
        }

        const question = new Question({
            companyId: company._id,
            createdBy: req.user.id,
            questionText: trimmedText,
            questionType,
            category,
            difficulty,
            options: options || [],
            correctAnswer,
            explanation,
            points: points || 1,
            isActive: isActive !== false
        });

        await question.save();

        logger.info(`Question created: ${questionType} - ${category} by company ${company.companyName}`);

        res.status(201).json({
            success: true,
            message: 'Question created successfully',
            question
        });
    } catch (error) {
        logger.error('Create question error:', error);
        next(error);
    }
};

const getQuestions = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const {
            page = 1,
            limit = 10,
            category,
            difficulty,
            questionType,
            search = '',
            sortBy = 'createdAt',
            sortOrder = 'desc',
            isActive
        } = req.query;

        const filter = { companyId: company._id };

        if (category) filter.category = category;
        if (difficulty) filter.difficulty = difficulty;
        if (questionType) filter.questionType = questionType;
        if (isActive !== undefined) filter.isActive = isActive === 'true';

        if (search) {
            filter.$or = [
                { questionText: { $regex: search, $options: 'i' } },
                { category: { $regex: search, $options: 'i' } },
                { tags: { $in: [new RegExp(search, 'i')] } }
            ];
        }

        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        const questions = await Question.find(filter)
            .sort(sortOptions)
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .populate('createdBy', 'name email')
            .lean();
        const total = await Question.countDocuments(filter);

        // Get question statistics
        const stats = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: null,
                    totalQuestions: { $sum: 1 },
                    activeQuestions: { $sum: { $cond: ['$isActive', 1, 0] } },
                    averagePoints: { $avg: '$points' },
                    averageTimeLimit: { $avg: '$timeLimit' }
                }
            }
        ]);

        res.json({
            success: true,
            questions,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            },
            statistics: stats[0] || {
                totalQuestions: 0,
                activeQuestions: 0,
                averagePoints: 0,
                averageTimeLimit: 0
            }
        });
    } catch (error) {
        logger.error('Get questions error:', error);
        next(error);
    }
};

const getQuestionById = async (req, res, next) => {
    try {
        const { questionId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(questionId)) {
            return res.status(400).json({ error: 'Invalid question ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const question = await Question.findOne({
            _id: questionId,
            companyId: company._id
        })
            .populate('createdBy', 'name email')
            .lean();

        if (!question) {
            return res.status(404).json({ error: 'Question not found' });
        }

        // Get tests that use this question
        const testsUsingQuestion = await Test.find({
            companyId: company._id,
            'questions.questionId': questionId
        }).select('testName createdAt').lean();

        res.json({
            success: true,
            question: {
                ...question,
                testsUsing: testsUsingQuestion
            }
        });
    } catch (error) {
        logger.error('Get question by ID error:', error);
        next(error);
    }
};

const updateQuestion = async (req, res, next) => {
    try {
        const { questionId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(questionId)) {
            return res.status(400).json({ error: 'Invalid question ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Validate input data
        const validationErrors = validateQuestionData(req.body);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check if question exists and belongs to the company
        const existingQuestion = await Question.findOne({
            _id: questionId,
            companyId: company._id
        });

        if (!existingQuestion) {
            return res.status(404).json({ error: 'Question not found' });
        }

        // Check for duplicate question with same text, type, category
        const questionText = req.body.questionText?.trim() || existingQuestion.questionText;
        const questionType = req.body.questionType || existingQuestion.questionType;
        const category = req.body.category || existingQuestion.category;

        const duplicateQuestion = await Question.findOne({
            _id: { $ne: questionId },
            companyId: company._id,
            questionText: questionText,
            questionType: questionType,
            category: category
        });

        if (duplicateQuestion) {
            return res.status(400).json({ error: 'A question with similar text, type, and category already exists.' });
        }

        const updateData = {
            ...req.body,
            questionText,  // Use trimmed
            updatedAt: new Date(),
            updatedBy: req.user.id
        };

        const question = await Question.findOneAndUpdate(
            { _id: questionId, companyId: company._id },
            updateData,
            { new: true, runValidators: true }
        ).populate('createdBy updatedBy', 'name email');

        logger.info(`Question updated: ${question.questionType} - ${question.category} by company ${company.companyName}`);

        res.json({
            success: true,
            message: 'Question updated successfully',
            question
        });
    } catch (error) {
        logger.error('Update question error:', error);
        next(error);
    }
};


const deleteQuestion = async (req, res, next) => {
    try {
        const { questionId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(questionId)) {
            return res.status(400).json({ error: 'Invalid question ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Correct way to check if the question is being used
        const testsUsingQuestion = await Test.find({
            companyId: company._id,
            'questions.questionId': questionId,
            isActive: true
        });

        if (testsUsingQuestion.length > 0) {
            return res.status(400).json({
                error: 'Question cannot be deleted as it is being used in active tests',
                testsUsing: testsUsingQuestion.map(test => test.testName)
            });
        }

        const question = await Question.findOneAndDelete({
            _id: questionId,
            companyId: company._id
        });

        if (!question) {
            return res.status(404).json({ error: 'Question not found' });
        }

        logger.info(`Question deleted: ${question.questionType} - ${question.category} by company ${company.companyName}`);

        res.json({
            success: true,
            message: 'Question deleted successfully'
        });
    } catch (error) {
        logger.error('Delete question error:', error);
        next(error);
    }
};

// Bulk operations
const bulkUpdateQuestions = async (req, res, next) => {
    try {
        const { questionIds, updateData } = req.body;

        if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
            return res.status(400).json({ error: 'Question IDs array is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const result = await Question.updateMany(
            {
                _id: { $in: questionIds },
                companyId: company._id
            },
            {
                ...updateData,
                updatedAt: new Date(),
                updatedBy: req.user.id
            }
        );

        res.json({
            success: true,
            message: `${result.modifiedCount} questions updated successfully`,
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        logger.error('Bulk update questions error:', error);
        next(error);
    }
};

const bulkDeleteQuestions = async (req, res, next) => {
    try {
        const { questionIds } = req.body;

        if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
            return res.status(400).json({ error: 'Question IDs array is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Check if any questions are being used in active tests
        const testsUsingQuestions = await Test.find({
            companyId: company._id,
            questions: { $in: questionIds },
            isActive: true
        });

        if (testsUsingQuestions.length > 0) {
            return res.status(400).json({
                error: 'Some questions cannot be deleted as they are being used in active tests',
                testsUsing: testsUsingQuestions.map(test => test.testName)
            });
        }

        const result = await Question.deleteMany({
            _id: { $in: questionIds },
            companyId: company._id
        });

        res.json({
            success: true,
            message: `${result.deletedCount} questions deleted successfully`,
            deletedCount: result.deletedCount
        });
    } catch (error) {
        logger.error('Bulk delete questions error:', error);
        next(error);
    }
};

// Analytics and reporting
const getQuestionAnalytics = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const analytics = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: null,
                    totalQuestions: { $sum: 1 },
                    activeQuestions: { $sum: { $cond: ['$isActive', 1, 0] } },
                    averageUsage: { $avg: '$usageCount' },
                    questionsByType: {
                        $push: {
                            type: '$questionType',
                            difficulty: '$difficulty',
                            category: '$category'
                        }
                    }
                }
            }
        ]);

        // Get distribution by type
        const typeDistribution = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: '$questionType',
                    count: { $sum: 1 },
                    averagePoints: { $avg: '$points' },
                    averageTimeLimit: { $avg: '$timeLimit' }
                }
            }
        ]);

        // Get distribution by difficulty
        const difficultyDistribution = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: '$difficulty',
                    count: { $sum: 1 },
                    averagePoints: { $avg: '$points' }
                }
            }
        ]);

        // Get distribution by category
        const categoryDistribution = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: '$category',
                    count: { $sum: 1 },
                    averageUsage: { $avg: '$usageCount' }
                }
            }
        ]);

        // Get most used questions
        const mostUsedQuestions = await Question.find({ companyId: company._id })
            .sort({ usageCount: -1 })
            .limit(10)
            .select('questionText questionType category difficulty usageCount')
            .lean();

        res.json({
            success: true,
            analytics: {
                overview: analytics[0] || {
                    totalQuestions: 0,
                    activeQuestions: 0,
                    averageUsage: 0
                },
                distributions: {
                    byType: typeDistribution,
                    byDifficulty: difficultyDistribution,
                    byCategory: categoryDistribution
                },
                mostUsedQuestions
            }
        });
    } catch (error) {
        logger.error('Get question analytics error:', error);
        next(error);
    }
};

// Import questions from template or file
const importQuestions = async (req, res, next) => {
    try {
        const { questions, template } = req.body;

        if (!questions || !Array.isArray(questions) || questions.length === 0) {
            return res.status(400).json({ error: 'Questions array is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const validationErrors = [];
        const validQuestions = [];

        // Validate each question
        questions.forEach((question, index) => {
            const errors = validateQuestionData(question);
            if (errors.length > 0) {
                validationErrors.push({ index, errors });
            } else {
                validQuestions.push({
                    ...question,
                    companyId: company._id,
                    createdBy: req.user.id,
                    usageCount: 0,
                    isActive: question.isActive !== false,
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
            }
        });

        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed for some questions',
                validationErrors
            });
        }

        // Check for duplicate questions
        const existingQuestions = await Question.find({
            companyId: company._id,
            questionText: { $in: validQuestions.map(q => q.questionText) }
        }).select('questionText');

        const existingTexts = existingQuestions.map(q => q.questionText);
        const uniqueQuestions = validQuestions.filter(q => !existingTexts.includes(q.questionText));

        if (uniqueQuestions.length === 0) {
            return res.status(400).json({ error: 'All questions already exist' });
        }

        const insertedQuestions = await Question.insertMany(uniqueQuestions);

        logger.info(`${insertedQuestions.length} questions imported by company ${company.companyName}`);

        res.json({
            success: true,
            message: `${insertedQuestions.length} questions imported successfully`,
            imported: insertedQuestions.length,
            duplicates: validQuestions.length - insertedQuestions.length,
            questions: insertedQuestions
        });
    } catch (error) {
        logger.error('Import questions error:', error);
        next(error);
    }
};

// Export questions
const exportQuestions = async (req, res, next) => {
    try {
        const { format = 'json', category, difficulty, questionType } = req.query;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const filter = { companyId: company._id };
        if (category) filter.category = category;
        if (difficulty) filter.difficulty = difficulty;
        if (questionType) filter.questionType = questionType;

        const questions = await Question.find(filter)
            .select('-companyId -createdBy -updatedBy -__v')
            .lean();

        if (format === 'csv') {
            // Convert to CSV format
            const csvHeader = 'Question Text,Type,Category,Difficulty,Options,Correct Answer,Points,Time Limit\n';
            const csvData = questions.map(q => {
                const options = q.options ? q.options.join(';') : '';
                return `"${q.questionText}","${q.questionType}","${q.category}","${q.difficulty}","${options}","${q.correctAnswer || ''}","${q.points}","${q.timeLimit}"`;
            }).join('\n');

            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', 'attachment; filename="questions.csv"');
            res.send(csvHeader + csvData);
        } else {
            res.json({
                success: true,
                questions,
                total: questions.length
            });
        }
    } catch (error) {
        logger.error('Export questions error:', error);
        next(error);
    }
};

// Duplicate question
const duplicateQuestion = async (req, res, next) => {
    try {
        const { questionId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(questionId)) {
            return res.status(400).json({ error: 'Invalid question ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const originalQuestion = await Question.findOne({
            _id: questionId,
            companyId: company._id
        }).lean();

        if (!originalQuestion) {
            return res.status(404).json({ error: 'Question not found' });
        }

        // Create duplicate with modified text
        const duplicateData = {
            ...originalQuestion,
            _id: undefined,
            questionText: `${originalQuestion.questionText} (Copy)`,
            createdBy: req.user.id,
            usageCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const duplicateQuestion = new Question(duplicateData);
        await duplicateQuestion.save();

        res.json({
            success: true,
            message: 'Question duplicated successfully',
            question: duplicateQuestion
        });
    } catch (error) {
        logger.error('Duplicate question error:', error);
        next(error);
    }
};

module.exports = {
    createQuestion,
    getQuestions,
    getQuestionById,
    updateQuestion,
    deleteQuestion,
    bulkUpdateQuestions,
    bulkDeleteQuestions,
    getQuestionAnalytics,
    importQuestions,
    exportQuestions,
    duplicateQuestion
};