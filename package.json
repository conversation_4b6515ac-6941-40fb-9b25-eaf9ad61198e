{"name": "backend", "version": "1.0.0", "description": "resumeBuilder Backend using Node.js and Express", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "build": "echo \"Nothing to build!\""}, "keywords": ["express", "backend", "resumeBuilder"], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.641.0", "@prisma/client": "^5.10.0", "axios": "^1.7.0", "bcrypt": "^5.1.1", "body-parser": "^2.2.0", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.2.2", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "dotenv": "^16.6.1", "express": "^4.21.2", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "fs": "^0.0.1-security", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.0", "mongoose": "^8.16.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-github2": "^0.1.11", "passport-google-oauth20": "^2.0.0", "pg": "^8.12.0", "puppeteer": "^22.15.0", "uuid": "^9.0.1", "winston": "^3.13.0"}, "devDependencies": {"eslint": "^9.0.0", "nodemon": "^3.1.0", "prettier": "^3.3.0"}}