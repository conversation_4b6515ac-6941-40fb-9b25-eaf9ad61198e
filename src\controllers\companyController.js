const Company = require('../models/Company');
const Job = require('../models/Job');
const User = require('../models/User');
const logger = require('../config/logger');
const mongoose = require('mongoose');
const Test = require('../models/Test');

// Input validation helper
function validateCompanyData(data) {
    const errors = [];

    if (!data.companyName || typeof data.companyName !== 'string' || !data.companyName.trim()) {
        errors.push('Company name is required and must be a non-empty string.');
    }

    if (!data.companyEmail || typeof data.companyEmail !== 'string' || !data.companyEmail.trim()) {
        errors.push('Valid company email is required.');
    }

    if (data.location && typeof data.location === 'object') {
        const { address, city, state, country, pincode } = data.location;
        if (!address || !city || !state || !country || !pincode) {
            errors.push('All location fields (address, city, state, country, pincode) are required.');
        }
    } else {
        errors.push('Location must be an object with required fields.');
    }

    if (data.contactPerson && typeof data.contactPerson === 'object') {
        const { name, designation, phone } = data.contactPerson;
        if (!name || !designation || !phone) {
            errors.push('Contact person name, designation, and phone are required.');
        }
    } else {
        errors.push('Contact person must be an object with required fields.');
    }

    return errors;
}
// Company Profile Management
const createCompanyProfile = async (req, res, next) => {
    try {
        const {
            companyName,
            companyEmail,
            website,
            description,
            industry,
            companySize,
            location,
            contactPerson,
            logo,
            socialLinks
        } = req.body;

        // Validate input data
        const validationErrors = validateCompanyData(req.body);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check if company already exists for this user
        const existingCompany = await Company.findOne({ userId: req.user.id });
        if (existingCompany) {
            return res.status(400).json({ error: 'Company profile already exists' });
        }

        // Check if company email is already taken
        const existingEmail = await Company.findOne({ companyEmail });
        if (existingEmail) {
            return res.status(400).json({ error: 'Company email already registered' });
        }

        const company = new Company({
            userId: req.user.id,
            companyName: companyName.trim(),
            companyEmail: companyEmail.toLowerCase().trim(),
            website,
            description,
            industry,
            companySize,
            location,
            contactPerson,
            logo,
            socialLinks,
            status: 'pending', // Default status
            createdAt: new Date(),
            updatedAt: new Date()
        });

        await company.save();

        // Log company creation
        logger.info(`Company profile created: ${companyName} by user ${req.user.id}`);

        res.status(201).json({
            success: true,
            message: 'Company profile created successfully',
            company: {
                ...company.toObject(),
                userId: undefined // Don't expose userId in response
            }
        });
    } catch (error) {
        logger.error('Create company profile error:', error);
        next(error);
    }
};

const getCompanyProfile = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id })
            .populate('userId', 'name email')
            .lean();

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Get additional statistics
        const totalJobs = await Job.countDocuments({ companyId: company._id });
        const activeJobs = await Job.countDocuments({ companyId: company._id, isActive: true });

        res.json({
            success: true,
            company: {
                ...company,
                statistics: {
                    totalJobs,
                    activeJobs
                }
            }
        });
    } catch (error) {
        logger.error('Get company profile error:', error);
        next(error);
    }
};

const updateCompanyProfile = async (req, res, next) => {
    try {
        // Validate input data
        const validationErrors = validateCompanyData(req.body);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check if email is being changed and if it's already taken
        if (req.body.companyEmail) {
            const existingEmail = await Company.findOne({
                companyEmail: req.body.companyEmail.toLowerCase().trim(),
                userId: { $ne: req.user.id }
            });
            if (existingEmail) {
                return res.status(400).json({ error: 'Company email already registered' });
            }
        }

        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };

        // Normalize email if provided
        if (updateData.companyEmail) {
            updateData.companyEmail = updateData.companyEmail.toLowerCase().trim();
        }

        const company = await Company.findOneAndUpdate(
            { userId: req.user.id },
            updateData,
            { new: true, runValidators: true }
        );

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        logger.info(`Company profile updated: ${company.companyName} by user ${req.user.id}`);

        res.json({
            success: true,
            message: 'Company profile updated successfully',
            company
        });
    } catch (error) {
        logger.error('Update company profile error:', error);
        next(error);
    }
};

// Job Management
const createJob = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        if (company.status !== 'approved') {
            return res.status(403).json({ error: 'Company not approved to post jobs' });
        }

        // Validate job data
        const { title, description, requirements, salary, location, jobType, experienceLevel } = req.body;

        if (!title || title.trim().length < 3) {
            return res.status(400).json({ error: 'Job title must be at least 3 characters long' });
        }

        if (!description || description.trim().length < 10) {
            return res.status(400).json({ error: 'Job description must be at least 10 characters long' });
        }

        const job = new Job({
            companyId: company._id,
            title: title.trim(),
            description: description.trim(),
            requirements: requirements || [],
            salary,
            location,
            jobType: jobType || 'full-time',
            experienceLevel: experienceLevel || 'entry',
            postedBy: req.user.id,
            createdAt: new Date(),
            updatedAt: new Date(),
            isActive: true,
            viewCount: 0,
            ...req.body
        });

        await job.save();

        logger.info(`Job created: ${title} by company ${company.companyName}`);

        res.status(201).json({
            success: true,
            message: 'Job posted successfully',
            job: await job.populate('companyId', 'companyName location logo')
        });
    } catch (error) {
        logger.error('Create job error:', error);
        next(error);
    }
};

const getCompanyJobs = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const {
            page = 1,
            limit = 10,
            status = 'all',
            search = '',
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;

        const filter = { companyId: company._id };

        if (status !== 'all') {
            filter.isActive = status === 'active';
        }

        if (search) {
            filter.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        const jobs = await Job.find(filter)
            .sort(sortOptions)
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .populate('testId', 'testName scheduledDate')
            .lean();

        const total = await Job.countDocuments(filter);

        // Add application count for each job
        const jobsWithStats = await Promise.all(
            jobs.map(async (job) => ({
                ...job,
                applicationCount: job.applicants ? job.applicants.length : 0,
                recentApplications: job.applicants ? job.applicants.slice(-3) : []
            }))
        );

        res.json({
            success: true,
            jobs: jobsWithStats,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            }
        });
    } catch (error) {
        logger.error('Get company jobs error:', error);
        next(error);
    }
};

const getJobDetails = async (req, res, next) => {
    try {
        const { jobId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOne({ _id: jobId, companyId: company._id })
            .populate('testId')
            .populate('applicants.candidateId', 'name email profile.resume profile.skills')
            .lean();

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        // Calculate application statistics
        const applicationStats = {
            total: job.applicants ? job.applicants.length : 0,
            pending: job.applicants ? job.applicants.filter(app => app.status === 'pending').length : 0,
            reviewed: job.applicants ? job.applicants.filter(app => app.status === 'reviewed').length : 0,
            shortlisted: job.applicants ? job.applicants.filter(app => app.status === 'shortlisted').length : 0,
            rejected: job.applicants ? job.applicants.filter(app => app.status === 'rejected').length : 0
        };

        res.json({
            success: true,
            job: {
                ...job,
                applicationStats
            }
        });
    } catch (error) {
        logger.error('Get job details error:', error);
        next(error);
    }
};

const updateJob = async (req, res, next) => {
    try {
        const { jobId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };

        const job = await Job.findOneAndUpdate(
            { _id: jobId, companyId: company._id },
            updateData,
            { new: true, runValidators: true }
        ).populate('companyId', 'companyName location logo');

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        logger.info(`Job updated: ${job.title} by company ${company.companyName}`);

        res.json({
            success: true,
            message: 'Job updated successfully',
            job
        });
    } catch (error) {
        logger.error('Update job error:', error);
        next(error);
    }
};

const deleteJob = async (req, res, next) => {
    try {
        const { jobId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOneAndDelete({ _id: jobId, companyId: company._id });

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        logger.info(`Job deleted: ${job.title} by company ${company.companyName}`);

        res.json({
            success: true,
            message: 'Job deleted successfully'
        });
    } catch (error) {
        logger.error('Delete job error:', error);
        next(error);
    }
};
//!Update job status
const updateJobStatus = async (req, res, next) => {
    try {
        const { jobId } = req.params;
        const { isActive } = req.body;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        if (typeof isActive !== 'boolean') {
            return res.status(400).json({ error: 'isActive must be a boolean' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOneAndUpdate(
            { _id: jobId, companyId: company._id },
            { isActive, updatedAt: new Date() },
            { new: true }
        );

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        logger.info(`Job status updated: ${job.title} is now ${isActive ? 'active' : 'inactive'}`);

        res.json({
            success: true,
            message: `Job status updated successfully`,
            job
        });
    } catch (error) {
        logger.error('Update job status error:', error);
        next(error);
    }
};

// Bulk operations
const bulkUpdateJobs = async (req, res, next) => {
    try {
        const { jobIds, updateData } = req.body;

        if (!jobIds || !Array.isArray(jobIds) || jobIds.length === 0) {
            return res.status(400).json({ error: 'Job IDs array is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const result = await Job.updateMany(
            {
                _id: { $in: jobIds },
                companyId: company._id
            },
            {
                ...updateData,
                updatedAt: new Date()
            }
        );

        res.json({
            success: true,
            message: `${result.modifiedCount} jobs updated successfully`,
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        logger.error('Bulk update jobs error:', error);
        next(error);
    }
};

// Application Management
const getJobApplications = async (req, res, next) => {
    try {
        const { jobId } = req.params;
        const {
            page = 1,
            limit = 10,
            status = 'all',
            sortBy = 'appliedAt',
            sortOrder = 'desc'
        } = req.query;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOne({ _id: jobId, companyId: company._id })
            .populate({
                path: 'applicants.candidateId',
                select: 'name email profile.resume profile.skills profile.experience'
            })
            .lean();

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        let applications = job.applicants || [];

        // Filter by status
        if (status !== 'all') {
            applications = applications.filter(app => app.status === status);
        }

        // Sort applications
        applications.sort((a, b) => {
            if (sortBy === 'appliedAt') {
                return sortOrder === 'desc'
                    ? new Date(b.appliedAt) - new Date(a.appliedAt)
                    : new Date(a.appliedAt) - new Date(b.appliedAt);
            }
            return 0;
        });

        // Pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedApplications = applications.slice(startIndex, endIndex);

        res.json({
            success: true,
            applications: paginatedApplications,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(applications.length / limit),
                total: applications.length
            },
            jobTitle: job.title
        });
    } catch (error) {
        logger.error('Get job applications error:', error);
        next(error);
    }
};

const updateApplicationStatus = async (req, res, next) => {
    try {
        const { jobId, candidateId } = req.params;
        const { status, notes } = req.body;

        if (!mongoose.Types.ObjectId.isValid(jobId) || !mongoose.Types.ObjectId.isValid(candidateId)) {
            return res.status(400).json({ error: 'Invalid job ID or candidate ID' });
        }

        const validStatuses = ['pending', 'reviewed', 'shortlisted', 'rejected', 'hired'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({ error: 'Invalid status' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOneAndUpdate(
            {
                _id: jobId,
                companyId: company._id,
                'applicants.candidateId': candidateId
            },
            {
                $set: {
                    'applicants.$.status': status,
                    'applicants.$.notes': notes,
                    'applicants.$.updatedAt': new Date()
                }
            },
            { new: true }
        );

        if (!job) {
            return res.status(404).json({ error: 'Job or application not found' });
        }

        logger.info(`Application status updated: ${status} for job ${job.title}`);

        res.json({
            success: true,
            message: 'Application status updated successfully'
        });
    } catch (error) {
        logger.error('Update application status error:', error);
        next(error);
    }
};

// Dashboard Analytics
const getCompanyDashboard = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const [totalJobs, activeJobs, jobs] = await Promise.all([
            Job.countDocuments({ companyId: company._id }),
            Job.countDocuments({ companyId: company._id, isActive: true }),
            Job.find({ companyId: company._id }).lean()
        ]);

        const totalApplications = jobs.reduce((sum, job) => sum + (job.applicants?.length || 0), 0);

        // Application status breakdown
        const applicationStats = {
            pending: 0,
            reviewed: 0,
            shortlisted: 0,
            rejected: 0,
            hired: 0
        };

        // Get recent applications with job details
        const recentApplications = [];
        jobs.forEach(job => {
            if (job.applicants) {
                job.applicants.forEach(app => {
                    recentApplications.push({
                        jobId: job._id,
                        jobTitle: job.title,
                        candidateId: app.candidateId,
                        candidateName: app.candidateId?.name || 'Unknown',
                        appliedAt: app.appliedAt,
                        status: app.status
                    });

                    if (applicationStats.hasOwnProperty(app.status)) {
                        applicationStats[app.status]++;
                    }
                });
            }
        });

        // Sort recent applications by date
        recentApplications.sort((a, b) => new Date(b.appliedAt) - new Date(a.appliedAt));

        // Job performance metrics
        const jobPerformance = jobs.map(job => ({
            jobId: job._id,
            title: job.title,
            applicationCount: job.applicants?.length || 0,
            viewCount: job.viewCount || 0,
            createdAt: job.createdAt,
            isActive: job.isActive
        }));

        res.json({
            success: true,
            dashboard: {
                totalJobs,
                activeJobs,
                totalApplications,
                applicationStats,
                recentApplications: recentApplications.slice(0, 10),
                jobPerformance: jobPerformance.slice(0, 5),
                companyStatus: company.status,
                companyProfile: {
                    name: company.companyName,
                    email: company.companyEmail,
                    location: company.location,
                    industry: company.industry
                }
            }
        });
    } catch (error) {
        logger.error('Get company dashboard error:', error);
        next(error);
    }
};

// New utility functions
const getJobAnalytics = async (req, res, next) => {
    try {
        const { jobId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOne({ _id: jobId, companyId: company._id }).lean();

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        const analytics = {
            totalViews: job.viewCount || 0,
            totalApplications: job.applicants?.length || 0,
            conversionRate: job.viewCount ? ((job.applicants?.length || 0) / job.viewCount * 100).toFixed(2) : 0,
            daysPosted: Math.ceil((new Date() - new Date(job.createdAt)) / (1000 * 60 * 60 * 24)),
            averageApplicationsPerDay: job.applicants?.length ? (job.applicants.length / Math.max(1, Math.ceil((new Date() - new Date(job.createdAt)) / (1000 * 60 * 60 * 24)))).toFixed(2) : 0
        };

        res.json({
            success: true,
            analytics
        });
    } catch (error) {
        logger.error('Get job analytics error:', error);
        next(error);
    }
};

module.exports = {
    createCompanyProfile,
    getCompanyProfile,
    updateCompanyProfile,
    createJob,
    getCompanyJobs,
    getJobDetails,
    updateJob,
    deleteJob,
    bulkUpdateJobs,
    getJobApplications,
    updateApplicationStatus,
    getCompanyDashboard,
    getJobAnalytics,
    updateJobStatus
};